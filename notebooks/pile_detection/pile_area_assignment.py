#!/usr/bin/env python3
"""
Pile Area Assignment Module for Castro Project

This module provides functionality to assign detected piles to specific tracker areas/zones
based on CAD reference data and spatial proximity analysis.

Key Features:
- Load tracker/area definitions from CAD metadata
- Assign detected piles to nearest tracker areas
- Validate pile counts against expected quantities per tracker
- Generate area-specific pile detection reports

Author: Preetam Balijepalli
Date: June 2025
Project: As-Built Foundation Analysis - Castro
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from scipy.spatial.distance import cdist
from scipy.spatial import cKDTree
import json
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TrackerAreaManager:
    """
    Manages tracker area definitions and pile assignments for Castro project.
    """
    
    def __init__(self, cad_metadata_path: str):
        """
        Initialize with CAD metadata containing tracker information.
        
        Parameters:
        -----------
        cad_metadata_path : str
            Path to CAD extraction results (CSV file with tracker data)
        """
        self.cad_metadata_path = Path(cad_metadata_path)
        self.tracker_areas = {}
        self.tracker_types = {}
        self.expected_piles_per_tracker = {
            'CVT_Tracker 1x52 int': 52,  # 1x52 internal tracker
            'CVT_Tracker 1x52 ext': 52,  # 1x52 external tracker  
            'CVT_Tracker 1X52 Edge': 52, # 1x52 edge tracker
            'CVT_Tracker 1x26 int': 26,  # 1x26 internal tracker
            'CVT_Tracker 1x26 ext': 26   # 1x26 external tracker
        }
        self.load_tracker_areas()
    
    def load_tracker_areas(self):
        """Load tracker area definitions from CAD metadata."""
        logger.info(f"Loading tracker areas from: {self.cad_metadata_path}")
        
        if not self.cad_metadata_path.exists():
            raise FileNotFoundError(f"CAD metadata file not found: {self.cad_metadata_path}")
        
        # Load CAD pile extraction data
        cad_df = pd.read_csv(self.cad_metadata_path)
        
        # Filter for tracker-related entities
        tracker_df = cad_df[cad_df['layer_name'].str.contains('CVT_Tracker', na=False)]
        
        logger.info(f"Found {len(tracker_df)} tracker entities in CAD data")
        
        # Group by tracker type and create area definitions
        for tracker_type in tracker_df['layer_name'].unique():
            tracker_entities = tracker_df[tracker_df['layer_name'] == tracker_type]
            
            self.tracker_areas[tracker_type] = {
                'coordinates': tracker_entities[['x_coord', 'y_coord', 'z_coord']].values,
                'entity_ids': tracker_entities['entity_id'].tolist(),
                'count': len(tracker_entities),
                'expected_piles': self.expected_piles_per_tracker.get(tracker_type, 0)
            }
            
            logger.info(f"Loaded {len(tracker_entities)} {tracker_type} trackers")
    
    def assign_piles_to_trackers(self, detected_piles: pd.DataFrame, 
                                max_distance: float = 50.0) -> pd.DataFrame:
        """
        Assign detected piles to tracker areas based on spatial proximity.
        
        Parameters:
        -----------
        detected_piles : pd.DataFrame
            DataFrame with detected pile coordinates (x, y, z columns required)
        max_distance : float
            Maximum distance (meters) for pile-to-tracker assignment
            
        Returns:
        --------
        pd.DataFrame
            Enhanced pile DataFrame with tracker assignments
        """
        logger.info(f"Assigning {len(detected_piles)} detected piles to tracker areas")
        
        # Initialize assignment columns
        detected_piles = detected_piles.copy()
        detected_piles['assigned_tracker_type'] = 'unassigned'
        detected_piles['assigned_tracker_id'] = None
        detected_piles['distance_to_tracker'] = np.inf
        detected_piles['tracker_area_id'] = None
        
        pile_coords = detected_piles[['x', 'y', 'z']].values
        
        # Assign piles to nearest tracker areas
        for tracker_type, tracker_info in self.tracker_areas.items():
            tracker_coords = tracker_info['coordinates']
            
            if len(tracker_coords) == 0:
                continue
            
            # Calculate distances from all piles to all trackers of this type
            distances = cdist(pile_coords[:, :2], tracker_coords[:, :2])  # Use X,Y only
            
            # Find nearest tracker for each pile
            nearest_tracker_indices = np.argmin(distances, axis=1)
            nearest_distances = np.min(distances, axis=1)
            
            # Assign piles within max_distance to this tracker type
            valid_assignments = nearest_distances <= max_distance
            
            for pile_idx in np.where(valid_assignments)[0]:
                current_distance = detected_piles.iloc[pile_idx]['distance_to_tracker']
                new_distance = nearest_distances[pile_idx]
                
                # Only assign if this is closer than previous assignment
                if new_distance < current_distance:
                    tracker_idx = nearest_tracker_indices[pile_idx]
                    tracker_id = tracker_info['entity_ids'][tracker_idx]
                    
                    detected_piles.iloc[pile_idx, detected_piles.columns.get_loc('assigned_tracker_type')] = tracker_type
                    detected_piles.iloc[pile_idx, detected_piles.columns.get_loc('assigned_tracker_id')] = tracker_id
                    detected_piles.iloc[pile_idx, detected_piles.columns.get_loc('distance_to_tracker')] = new_distance
                    detected_piles.iloc[pile_idx, detected_piles.columns.get_loc('tracker_area_id')] = f"{tracker_type}_{tracker_id}"
        
        # Generate assignment statistics
        assignment_stats = self.generate_assignment_statistics(detected_piles)
        logger.info("Pile assignment completed")
        
        return detected_piles, assignment_stats
    
    def generate_assignment_statistics(self, assigned_piles: pd.DataFrame) -> Dict[str, Any]:
        """Generate statistics about pile assignments."""
        stats = {
            'total_piles': len(assigned_piles),
            'assigned_piles': len(assigned_piles[assigned_piles['assigned_tracker_type'] != 'unassigned']),
            'unassigned_piles': len(assigned_piles[assigned_piles['assigned_tracker_type'] == 'unassigned']),
            'tracker_type_counts': {},
            'expected_vs_actual': {},
            'assignment_quality': {}
        }
        
        # Count piles by tracker type
        for tracker_type in assigned_piles['assigned_tracker_type'].unique():
            if tracker_type == 'unassigned':
                continue
            
            type_piles = assigned_piles[assigned_piles['assigned_tracker_type'] == tracker_type]
            stats['tracker_type_counts'][tracker_type] = len(type_piles)
            
            # Calculate expected vs actual
            expected_total = self.tracker_areas[tracker_type]['count'] * self.expected_piles_per_tracker.get(tracker_type, 0)
            actual_total = len(type_piles)
            
            stats['expected_vs_actual'][tracker_type] = {
                'expected_total': expected_total,
                'actual_total': actual_total,
                'detection_rate': actual_total / expected_total if expected_total > 0 else 0,
                'trackers_count': self.tracker_areas[tracker_type]['count'],
                'expected_per_tracker': self.expected_piles_per_tracker.get(tracker_type, 0)
            }
            
            # Assignment quality metrics
            avg_distance = type_piles['distance_to_tracker'].mean()
            max_distance = type_piles['distance_to_tracker'].max()
            
            stats['assignment_quality'][tracker_type] = {
                'avg_distance_to_tracker': avg_distance,
                'max_distance_to_tracker': max_distance,
                'piles_within_10m': len(type_piles[type_piles['distance_to_tracker'] <= 10.0]),
                'piles_within_25m': len(type_piles[type_piles['distance_to_tracker'] <= 25.0])
            }
        
        return stats
    
    def validate_pile_counts(self, assigned_piles: pd.DataFrame) -> Dict[str, Any]:
        """
        Validate detected pile counts against expected quantities.
        
        Returns validation report with discrepancies and recommendations.
        """
        validation_report = {
            'overall_status': 'unknown',
            'total_expected': 0,
            'total_detected': 0,
            'tracker_validations': {},
            'recommendations': []
        }
        
        total_expected = 0
        total_detected = len(assigned_piles[assigned_piles['assigned_tracker_type'] != 'unassigned'])
        
        for tracker_type, tracker_info in self.tracker_areas.items():
            expected_per_tracker = self.expected_piles_per_tracker.get(tracker_type, 0)
            tracker_count = tracker_info['count']
            expected_total = tracker_count * expected_per_tracker
            
            type_piles = assigned_piles[assigned_piles['assigned_tracker_type'] == tracker_type]
            detected_total = len(type_piles)
            
            total_expected += expected_total
            
            validation_report['tracker_validations'][tracker_type] = {
                'tracker_count': tracker_count,
                'expected_per_tracker': expected_per_tracker,
                'expected_total': expected_total,
                'detected_total': detected_total,
                'detection_rate': detected_total / expected_total if expected_total > 0 else 0,
                'status': 'good' if abs(detected_total - expected_total) / expected_total < 0.1 else 'needs_review'
            }
        
        validation_report['total_expected'] = total_expected
        validation_report['total_detected'] = total_detected
        validation_report['overall_detection_rate'] = total_detected / total_expected if total_expected > 0 else 0
        
        # Generate recommendations
        if validation_report['overall_detection_rate'] < 0.8:
            validation_report['recommendations'].append("Low overall detection rate - consider adjusting detection parameters")
        
        unassigned_count = len(assigned_piles[assigned_piles['assigned_tracker_type'] == 'unassigned'])
        if unassigned_count > 0:
            validation_report['recommendations'].append(f"{unassigned_count} piles unassigned - consider increasing max_distance parameter")
        
        validation_report['overall_status'] = 'good' if validation_report['overall_detection_rate'] > 0.8 else 'needs_review'
        
        return validation_report
    
    def export_area_assignments(self, assigned_piles: pd.DataFrame, 
                               output_dir: Path, site_name: str) -> Dict[str, Path]:
        """Export pile assignments to various formats."""
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")
        exported_files = {}
        
        # 1. Main assignment file
        main_file = output_dir / f"{site_name}_pile_assignments_{timestamp}.csv"
        assigned_piles.to_csv(main_file, index=False)
        exported_files['assignments'] = main_file
        
        # 2. Summary by tracker type
        summary_data = []
        for tracker_type in assigned_piles['assigned_tracker_type'].unique():
            if tracker_type == 'unassigned':
                continue
            
            type_piles = assigned_piles[assigned_piles['assigned_tracker_type'] == tracker_type]
            summary_data.append({
                'tracker_type': tracker_type,
                'detected_piles': len(type_piles),
                'avg_distance_to_tracker': type_piles['distance_to_tracker'].mean(),
                'trackers_with_piles': type_piles['assigned_tracker_id'].nunique()
            })
        
        summary_df = pd.DataFrame(summary_data)
        summary_file = output_dir / f"{site_name}_tracker_summary_{timestamp}.csv"
        summary_df.to_csv(summary_file, index=False)
        exported_files['summary'] = summary_file
        
        # 3. Validation report
        validation_report = self.validate_pile_counts(assigned_piles)
        validation_file = output_dir / f"{site_name}_validation_report_{timestamp}.json"
        with open(validation_file, 'w') as f:
            json.dump(validation_report, f, indent=2)
        exported_files['validation'] = validation_file
        
        logger.info(f"Exported pile assignments to {len(exported_files)} files")
        return exported_files


def load_detected_piles(pile_detection_file: str) -> pd.DataFrame:
    """
    Load detected piles from pile detection results.
    
    Supports various pile detection output formats.
    """
    pile_file = Path(pile_detection_file)
    
    if not pile_file.exists():
        raise FileNotFoundError(f"Pile detection file not found: {pile_file}")
    
    # Load pile detection results
    piles_df = pd.read_csv(pile_file)
    
    # Ensure required columns exist
    required_columns = ['x', 'y', 'z']
    missing_columns = [col for col in required_columns if col not in piles_df.columns]
    
    if missing_columns:
        raise ValueError(f"Missing required columns in pile detection file: {missing_columns}")
    
    logger.info(f"Loaded {len(piles_df)} detected piles from {pile_file}")
    return piles_df


def main():
    """Example usage of the pile area assignment system."""
    
    # Configuration
    cad_metadata_path = "../data/raw/motali_de_castro/cad/enhanced_output/cad_extraction_pile_20250630_143601.csv"
    pile_detection_file = "../output_runs/pile_detection/castro_detected_piles.csv"  # Example path
    output_dir = "../output_runs/pile_detection/area_assignments"
    site_name = "castro"
    max_assignment_distance = 30.0  # meters
    
    try:
        # Initialize tracker area manager
        tracker_manager = TrackerAreaManager(cad_metadata_path)
        
        # Load detected piles (this would come from your pile detection notebooks)
        # For demonstration, create sample data
        sample_piles = pd.DataFrame({
            'pile_id': [f'P{i:03d}' for i in range(1, 101)],
            'x': np.random.uniform(707000, 708000, 100),
            'y': np.random.uniform(4692800, 4693200, 100),
            'z': np.random.uniform(1.0, 3.0, 100),
            'confidence': np.random.uniform(0.7, 0.95, 100),
            'pile_type': ['C-section'] * 100
        })
        
        # Assign piles to tracker areas
        assigned_piles, assignment_stats = tracker_manager.assign_piles_to_trackers(
            sample_piles, max_distance=max_assignment_distance
        )
        
        # Export results
        exported_files = tracker_manager.export_area_assignments(
            assigned_piles, output_dir, site_name
        )
        
        # Print summary
        print("\n=== PILE AREA ASSIGNMENT SUMMARY ===")
        print(f"Total piles processed: {assignment_stats['total_piles']}")
        print(f"Successfully assigned: {assignment_stats['assigned_piles']}")
        print(f"Unassigned piles: {assignment_stats['unassigned_piles']}")
        
        print("\nAssignments by tracker type:")
        for tracker_type, count in assignment_stats['tracker_type_counts'].items():
            expected_info = assignment_stats['expected_vs_actual'][tracker_type]
            print(f"  {tracker_type}: {count} piles (expected: {expected_info['expected_total']})")
        
        print(f"\nResults exported to: {output_dir}")
        
    except Exception as e:
        logger.error(f"Error in pile area assignment: {e}")
        raise


if __name__ == "__main__":
    main()
